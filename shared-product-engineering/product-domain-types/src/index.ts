// /**
//  * @beauty-crm/platform-domain-types
//  * Shared domain types for the Beauty CRM system
//  */

export * from './base';
export * from './business';
export * from './contact';
export * from './salon/config';
export * from './salon/identity';
export * from './salon/interfaces';
export * from './salon/settings';
// Export Salon types that depend on shared types
export * from './salon/templates/demo';
export * from './salon/types';
export * from './schedule';
export * from './shared';
// Export shared types first as they have no dependencies
export * from './shared/types';
export * from './staff';
export * from './treatment';
export * from './user';
export * from './workflow';
// Note: For system-related types, please use:
