import type { BaseEntity } from '../base/base-entity';
import type { SalonId } from '../base/types';
import type { ContactInfo } from '../contact/types';
import type { LoyaltyMember } from '../loyalty/types';
import type { NotificationPreferences } from '../notification/types';

export type ClientStatus = 'active' | 'inactive' | 'blocked';

export type ClientSource =
  | 'referral'
  | 'walk_in'
  | 'online'
  | 'social_media'
  | 'advertisement'
  | 'other';

export interface Client extends BaseEntity {
  salonId: SalonId;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  status: ClientStatus;
  source?: ClientSource;
  contact: ContactInfo;
  notificationPreferences: NotificationPreferences;
  notes?: string;
  tags?: string[];
  loyalty?: LoyaltyMember;
  totalSpent?: number;
  lastVisit?: Date;
  metadata?: Record<string, unknown>;
}

export interface ClientPreferences {
  preferredStaff?: string[];
  preferredServices?: string[];
  preferredDays?: string[];
  preferredTimes?: {
    start: string; // HH:mm format
    end: string; // HH:mm format
  };
  allergies?: string[];
  specialRequests?: string[];
  notes?: string;
}

export interface ClientReferral {
  customerId: string;
  referredBy?: string;
  referralCode: string;
  referrals: Array<{
    referredCustomerId: string;
    date: Date;
    status: 'pending' | 'completed';
    reward?: {
      type: string;
      value: number;
      claimed: boolean;
      claimedAt?: Date;
    };
  }>;
}

export interface ClientFeedback {
  customerId: string;
  serviceId: string; // Generic service identifier instead of appointmentId
  rating: number;
  comment?: string;
  categories?: {
    serviceQuality?: number;
    staffProfessionalism?: number;
    facilityCleanness?: number;
    overallExperience?: number;
  };
  createdAt: Date;
  updatedAt?: Date;
  isPublic: boolean;
}

export interface ClientWaitlist {
  customerId: string;
  treatmentId: string;
  preferredStaffId?: string;
  preferredDays?: string[];
  preferredTimes?: {
    start: string; // HH:mm format
    end: string; // HH:mm format
  };
  priority: number;
  notes?: string;
  createdAt: Date;
  notificationsSent: number;
  lastNotificationAt?: Date;
  status: 'active' | 'fulfilled' | 'cancelled';
}
