/**
 * 📦 Outbox Usage Example
 *
 * Demonstrates how to use the transactional outbox pattern with SimpleAggregate
 */

import { PrismaClient } from '@prisma/client';
import {
  BaseAggregate,
  type Command,
  createPrismaOutboxStorage,
  type DomainEvent,
  OutboxManager,
} from '../src/index';

// ============================================================================
// Example Appointment Aggregate
// ============================================================================

interface AppointmentState {
  id: string;
  customerId: string;
  staffId: string;
  treatmentId: string;
  scheduledAt: Date;
  status: 'SCHEDULED' | 'CONFIRMED' | 'CANCELLED' | 'COMPLETED';
  notes?: string;
}

interface CreateAppointmentCommand extends Command {
  commandType: 'create-appointment';
  data: {
    customerId: string;
    staffId: string;
    treatmentId: string;
    scheduledAt: Date;
    notes?: string;
  };
}

class AppointmentAggregate extends BaseAggregate<AppointmentState> {
  protected getAggregateType(): string {
    return 'appointment';
  }

  protected getSource(): string {
    return 'appointment-planner-backend';
  }

  processCommand(command: Command): DomainEvent[] {
    this.validateCommand(command);

    switch (command.commandType) {
      case 'create-appointment':
        return this.handleCreateAppointment(
          command as CreateAppointmentCommand,
        );
      default:
        this.throwDomainError(`Unknown command type: ${command.commandType}`);
    }
  }

  private handleCreateAppointment(
    command: CreateAppointmentCommand,
  ): DomainEvent[] {
    // Validate business rules
    if (this.state) {
      this.throwDomainError('Appointment already exists');
    }

    const { customerId, staffId, treatmentId, scheduledAt, notes } =
      command.data;

    // Apply state change
    this.applyStateChange(() => ({
      customerId,
      id: this.id,
      notes,
      scheduledAt,
      staffId,
      status: 'SCHEDULED',
      treatmentId,
    }));

    // Create domain event
    const event = this.createEventFromCommand(
      'appointment.created',
      {
        appointmentId: this.id,
        customerId,
        notes,
        scheduledAt: scheduledAt.toISOString(),
        staffId,
        status: 'SCHEDULED',
        treatmentId,
      },
      command,
    );

    return [event];
  }
}

// ============================================================================
// Usage Example
// ============================================================================

async function exampleUsage() {
  // 1. Set up Prisma client and outbox storage
  const prisma = new PrismaClient();
  const outboxStorage = createPrismaOutboxStorage(
    prisma,
    'appointment-planner-backend',
  );
  const outboxManager = new OutboxManager(outboxStorage, {
    batchSize: 50,
    maxRetries: 3,
    retentionHours: 48,
    source: 'appointment-planner-backend',
  });

  // 2. Create appointment aggregate with outbox support
  const appointmentId = 'appointment-123';
  const appointment = new AppointmentAggregate(
    appointmentId,
    undefined, // no initial state
    outboxManager,
  );

  // 3. Create command
  const command: CreateAppointmentCommand = {
    commandType: 'create-appointment',
    data: {
      customerId: 'customer-456',
      notes: 'First appointment for facial treatment',
      scheduledAt: new Date('2024-01-15T10:00:00Z'),
      staffId: 'staff-789',
      treatmentId: 'treatment-101',
    },
    metadata: {
      correlationId: 'req-abc123',
      timestamp: new Date(),
      userId: 'user-999',
    },
  };

  // 4. Process command with outbox (within transaction)
  try {
    await prisma.$transaction(async (tx) => {
      // Process command and store events atomically
      const result = await appointment.processCommandWithOutbox(command, tx);

      if (!result.success) {
        throw new Error(result.error);
      }

      console.log('✅ Command processed successfully:', result);
      console.log('📦 Events stored in outbox:', result.events);
    });
  } catch (error) {
    console.error('❌ Command processing failed:', error);
  }

  // 5. Retrieve unprocessed events (for Debezium or manual processing)
  const unprocessedEvents = await outboxManager.getUnprocessedEvents(10);
  console.log('📤 Unprocessed events:', unprocessedEvents.length);

  // 6. Simulate event processing (normally done by Debezium)
  if (unprocessedEvents.length > 0) {
    const eventIds = unprocessedEvents.map((e) => e.eventId);

    try {
      // Simulate publishing to NATS
      console.log('📡 Publishing events to NATS...');

      // Mark events as processed
      await outboxManager.markEventsProcessed(eventIds);
      console.log('✅ Events marked as processed');
    } catch (error) {
      // Mark events as failed
      for (const event of unprocessedEvents) {
        await outboxManager.markEventFailed(event.eventId, error as Error);
      }
      console.error('❌ Event processing failed:', error);
    }
  }

  // 7. Get outbox statistics
  const stats = await outboxManager.getStats();
  console.log('📊 Outbox statistics:', stats);

  // 8. Health check
  const health = await outboxManager.healthCheck();
  console.log('🏥 Outbox health:', health);

  // 9. Cleanup old events (normally done by scheduled job)
  const cleanedCount = await outboxManager.cleanupProcessedEvents();
  console.log(`🧹 Cleaned up ${cleanedCount} old processed events`);

  await prisma.$disconnect();
}

// ============================================================================
// Alternative: Using Aggregate without Outbox Manager
// ============================================================================

async function _exampleWithoutOutboxManager() {
  const prisma = new PrismaClient();

  // Create aggregate without outbox manager
  const appointment = new AppointmentAggregate('appointment-456');

  const command: CreateAppointmentCommand = {
    commandType: 'create-appointment',
    data: {
      customerId: 'customer-789',
      scheduledAt: new Date('2024-01-16T14:00:00Z'),
      staffId: 'staff-101',
      treatmentId: 'treatment-202',
    },
    metadata: {
      correlationId: 'req-def456',
      timestamp: new Date(),
    },
  };

  // Process command and manually store events
  const result = appointment.processCommandWithResult(command);

  if (result.success) {
    // Manually create outbox manager and store events
    const outboxStorage = createPrismaOutboxStorage(
      prisma,
      'appointment-planner-backend',
    );
    const outboxManager = new OutboxManager(outboxStorage, {
      source: 'appointment-planner-backend',
    });

    // Get the events from the aggregate (you'd need to modify the aggregate to expose them)
    // For now, we'll create a dummy event
    const events = [
      {
        aggregateId: appointment.id,
        aggregateType: 'appointment',
        data: { test: 'data' },
        eventId: 'event-123',
        eventType: 'appointment.created',
        eventVersion: 1,
        source: 'appointment-planner-backend',
        timestamp: new Date().toISOString(),
      },
    ];

    await outboxManager.storeEvents(events);
    console.log('📦 Events stored manually in outbox');
  }

  await prisma.$disconnect();
}

// Run examples
if (require.main === module) {
  console.log('🚀 Running outbox usage examples...\n');

  exampleUsage()
    .then(() => console.log('\n✅ Example completed successfully'))
    .catch((error) => console.error('\n❌ Example failed:', error));
}
