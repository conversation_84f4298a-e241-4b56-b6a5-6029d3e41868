{"compilerOptions": {"allowSyntheticDefaultImports": true, "declaration": true, "declarationMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "lib": ["ES2020"], "module": "CommonJS", "moduleResolution": "node", "outDir": "./dist", "resolveJsonModule": true, "rootDir": "./src", "skipLibCheck": true, "sourceMap": true, "strict": true, "target": "ES2020", "types": ["node"]}, "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "include": ["src/**/*"]}