<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Beauty CRM - Staff Management Dashboard</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: #f5f7fa;
        color: #333;
        overflow-x: hidden;
      }

      .sidebar {
        position: fixed;
        left: 0;
        top: 0;
        width: 260px;
        height: 100vh;
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: white;
        padding: 20px 0;
        z-index: 1000;
        box-shadow: 3px 0 15px rgba(0, 0, 0, 0.1);
      }

      .logo {
        text-align: center;
        padding: 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        margin-bottom: 30px;
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0%,
        100% {
          transform: scale(1);
        }
        50% {
          transform: scale(1.05);
        }
      }

      .logo h2 {
        color: #e74c3c;
        font-size: 1.5em;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      .nav-item {
        padding: 15px 25px;
        cursor: pointer;
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
        position: relative;
        overflow: hidden;
      }

      .nav-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.1),
          transparent
        );
        transition: left 0.5s;
      }

      .nav-item:hover::before {
        left: 100%;
      }

      .nav-item:hover {
        background: rgba(255, 255, 255, 0.1);
        border-left-color: #e74c3c;
        transform: translateX(5px);
      }

      .nav-item.active {
        background: rgba(231, 76, 60, 0.2);
        border-left-color: #e74c3c;
        box-shadow: inset 0 0 10px rgba(231, 76, 60, 0.3);
      }

      .nav-item i {
        margin-right: 10px;
        width: 20px;
        font-size: 1.2em;
      }

      .notification-badge {
        background: #e74c3c;
        color: white;
        border-radius: 50%;
        padding: 2px 6px;
        font-size: 0.7em;
        margin-left: auto;
        animation: bounce 1s infinite;
      }

      @keyframes bounce {
        0%,
        20%,
        50%,
        80%,
        100% {
          transform: translateY(0);
        }
        40% {
          transform: translateY(-5px);
        }
        60% {
          transform: translateY(-3px);
        }
      }

      .main-content {
        margin-left: 260px;
        padding: 20px;
        transition: all 0.3s ease;
      }

      .header {
        background: white;
        padding: 20px 30px;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        overflow: hidden;
      }

      .header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #e74c3c, #f39c12, #e74c3c);
        animation: shimmer 2s infinite;
      }

      @keyframes shimmer {
        0% {
          transform: translateX(-100%);
        }
        100% {
          transform: translateX(100%);
        }
      }

      .header h1 {
        color: #2c3e50;
        font-size: 1.8em;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }

      .date-picker {
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .date-picker input {
        padding: 8px 12px;
        border: 2px solid #ddd;
        border-radius: 8px;
        font-size: 14px;
        transition: all 0.3s ease;
      }

      .date-picker input:focus {
        border-color: #e74c3c;
        box-shadow: 0 0 10px rgba(231, 76, 60, 0.3);
        outline: none;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
        margin-bottom: 30px;
      }

      .stat-card {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        text-align: center;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        cursor: pointer;
      }

      .stat-card::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(
          45deg,
          transparent,
          rgba(231, 76, 60, 0.1),
          transparent
        );
        transform: rotate(45deg);
        transition: all 0.6s;
        opacity: 0;
      }

      .stat-card:hover::before {
        opacity: 1;
        animation: shine 0.6s ease-in-out;
      }

      @keyframes shine {
        0% {
          transform: translateX(-100%) translateY(-100%) rotate(45deg);
        }
        100% {
          transform: translateX(100%) translateY(100%) rotate(45deg);
        }
      }

      .stat-card:hover {
        transform: translateY(-10px) scale(1.05);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
      }

      .stat-card .icon {
        font-size: 2.5em;
        margin-bottom: 10px;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
      }

      .stat-card .number {
        font-size: 2em;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 5px;
        transition: all 0.3s ease;
      }

      .stat-card:hover .number {
        color: #e74c3c;
        transform: scale(1.1);
      }

      .stat-card .label {
        color: #7f8c8d;
        font-size: 0.9em;
        font-weight: 500;
      }

      .content-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 30px;
      }

      .calendar-section,
      .appointments-section {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        padding: 25px;
        transition: all 0.3s ease;
      }

      .calendar-section:hover,
      .appointments-section:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 2px solid #f8f9fa;
        position: relative;
      }

      .section-header::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 50px;
        height: 2px;
        background: #e74c3c;
        animation: expand 2s ease-in-out infinite;
      }

      @keyframes expand {
        0%,
        100% {
          width: 50px;
        }
        50% {
          width: 100px;
        }
      }

      .section-header h3 {
        color: #2c3e50;
        font-size: 1.3em;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      .btn {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 600;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: left 0.5s;
      }

      .btn:hover::before {
        left: 100%;
      }

      .btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(231, 76, 60, 0.4);
      }

      .btn:active {
        transform: translateY(-1px);
      }

      .calendar-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 3px;
        margin-top: 20px;
      }

      .calendar-day {
        aspect-ratio: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f8f9fa;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        font-weight: 500;
      }

      .calendar-day::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, #e74c3c, #f39c12);
        border-radius: 8px;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .calendar-day:hover::before {
        opacity: 0.1;
      }

      .calendar-day:hover {
        transform: scale(1.1);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      }

      .calendar-day.has-appointments {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        box-shadow: 0 3px 10px rgba(52, 152, 219, 0.3);
      }

      .calendar-day.has-appointments::after {
        content: '•';
        position: absolute;
        bottom: 2px;
        right: 2px;
        color: #f39c12;
        font-size: 1.2em;
        animation: pulse 1.5s infinite;
      }

      .calendar-day.selected {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        color: white;
        transform: scale(1.05);
        box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
      }

      .appointment-item {
        padding: 15px;
        border-bottom: 1px solid #f1f2f6;
        transition: all 0.3s ease;
        cursor: pointer;
        border-radius: 8px;
        margin-bottom: 5px;
        position: relative;
        overflow: hidden;
      }

      .appointment-item::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        background: #e74c3c;
        transform: scaleY(0);
        transition: transform 0.3s ease;
      }

      .appointment-item:hover::before {
        transform: scaleY(1);
      }

      .appointment-item:hover {
        background: linear-gradient(135deg, #f8f9fa, #e8f4f8);
        transform: translateX(5px);
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
      }

      .appointment-time {
        font-weight: bold;
        color: #e74c3c;
        font-size: 0.9em;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      .appointment-client {
        font-weight: 600;
        margin-bottom: 5px;
        font-size: 1.05em;
      }

      .appointment-service {
        color: #7f8c8d;
        font-size: 0.9em;
      }

      .appointment-status {
        padding: 4px 10px;
        border-radius: 20px;
        font-size: 0.75em;
        font-weight: 600;
        float: right;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      }

      .status-confirmed {
        background: linear-gradient(135deg, #27ae60, #2ecc71);
        color: white;
        animation: glow-green 2s ease-in-out infinite alternate;
      }

      @keyframes glow-green {
        from {
          box-shadow: 0 0 5px rgba(39, 174, 96, 0.5);
        }
        to {
          box-shadow: 0 0 20px rgba(39, 174, 96, 0.8);
        }
      }

      .status-pending {
        background: linear-gradient(135deg, #f39c12, #e67e22);
        color: white;
        animation: glow-orange 2s ease-in-out infinite alternate;
      }

      @keyframes glow-orange {
        from {
          box-shadow: 0 0 5px rgba(243, 156, 18, 0.5);
        }
        to {
          box-shadow: 0 0 20px rgba(243, 156, 18, 0.8);
        }
      }

      .status-completed {
        background: linear-gradient(135deg, #3498db, #2980b9);
        color: white;
        animation: glow-blue 2s ease-in-out infinite alternate;
      }

      @keyframes glow-blue {
        from {
          box-shadow: 0 0 5px rgba(52, 152, 219, 0.5);
        }
        to {
          box-shadow: 0 0 20px rgba(52, 152, 219, 0.8);
        }
      }

      .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 2000;
        backdrop-filter: blur(5px);
        animation: fadeIn 0.3s ease;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
        }
        to {
          opacity: 1;
        }
      }

      .modal-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        border-radius: 15px;
        padding: 30px;
        width: 500px;
        max-width: 90vw;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        animation: slideIn 0.3s ease;
      }

      @keyframes slideIn {
        from {
          transform: translate(-50%, -50%) scale(0.8);
          opacity: 0;
        }
        to {
          transform: translate(-50%, -50%) scale(1);
          opacity: 1;
        }
      }

      .form-group {
        margin-bottom: 20px;
      }

      .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: #2c3e50;
      }

      .form-group input,
      .form-group select,
      .form-group textarea {
        width: 100%;
        padding: 12px;
        border: 2px solid #ddd;
        border-radius: 8px;
        font-size: 14px;
        transition: all 0.3s ease;
      }

      .form-group input:focus,
      .form-group select:focus,
      .form-group textarea:focus {
        border-color: #e74c3c;
        box-shadow: 0 0 10px rgba(231, 76, 60, 0.3);
        outline: none;
        transform: scale(1.02);
      }

      .form-group textarea {
        resize: vertical;
        height: 80px;
      }

      .modal-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 20px;
      }

      .btn-secondary {
        background: linear-gradient(135deg, #95a5a6, #7f8c8d);
      }

      .btn-secondary:hover {
        background: linear-gradient(135deg, #7f8c8d, #95a5a6);
      }

      .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 3000;
      }

      .spinner {
        width: 50px;
        height: 50px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #e74c3c;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #27ae60, #2ecc71);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        z-index: 4000;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
      }

      .notification.show {
        opacity: 1;
        transform: translateX(0);
      }

      .real-time-indicator {
        display: inline-block;
        width: 8px;
        height: 8px;
        background: #27ae60;
        border-radius: 50%;
        margin-left: 10px;
        animation: pulse 1s infinite;
      }
    </style>
  </head>
  <body>
    <div class="loading-overlay" id="loadingOverlay">
      <div class="spinner"></div>
    </div>

    <div class="notification" id="notification"></div>

    <!-- Sidebar -->
    <div class="sidebar">
      <div class="logo">
        <h2>🌸 Beauty CRM</h2>
        <p>Staff Dashboard <span class="real-time-indicator"></span></p>
      </div>

      <div class="nav-item active" data-section="dashboard">
        <i>📊</i> Dashboard
      </div>
      <div class="nav-item" data-section="calendar"><i>📅</i> Calendar</div>
      <div class="nav-item" data-section="appointments">
        <i>📋</i> Appointments
        <span class="notification-badge">3</span>
      </div>
      <div class="nav-item" data-section="clients"><i>👥</i> Clients</div>
      <div class="nav-item" data-section="services"><i>💅</i> Services</div>
      <div class="nav-item" data-section="reports"><i>📈</i> Reports</div>
      <div class="nav-item" data-section="settings"><i>⚙️</i> Settings</div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
      <div class="header">
        <h1 id="pageTitle">Dashboard Overview</h1>
        <div class="date-picker">
          <label>Date:</label>
          <input type="date" id="currentDate" value="2024-05-24" />
          <button class="btn" id="todayBtn">Today</button>
        </div>
      </div>

      <!-- Dashboard Stats -->
      <div class="stats-grid" id="statsSection">
        <div class="stat-card" data-metric="appointments">
          <div class="icon">📅</div>
          <div class="number" id="todayAppointments">12</div>
          <div class="label">Today's Appointments</div>
        </div>
        <div class="stat-card" data-metric="pending">
          <div class="icon">⏰</div>
          <div class="number" id="pendingCount">3</div>
          <div class="label">Pending Confirmations</div>
        </div>
        <div class="stat-card" data-metric="revenue">
          <div class="icon">💰</div>
          <div class="number" id="todayRevenue">€1,245</div>
          <div class="label">Today's Revenue</div>
        </div>
        <div class="stat-card" data-metric="clients">
          <div class="icon">👥</div>
          <div class="number" id="newClients">8</div>
          <div class="label">New Clients</div>
        </div>
      </div>

      <!-- Content Grid -->
      <div class="content-grid">
        <!-- Calendar Section -->
        <div class="calendar-section">
          <div class="section-header">
            <h3>📅 Calendar View</h3>
            <button class="btn" id="newAppointmentBtn">
              + New Appointment
            </button>
          </div>

          <div class="calendar-header">
            <div
              style="
                display: grid;
                grid-template-columns: repeat(7, 1fr);
                gap: 3px;
                margin-bottom: 10px;
                font-weight: bold;
                text-align: center;
                color: #7f8c8d;
              "
            >
              <div>Mon</div>
              <div>Tue</div>
              <div>Wed</div>
              <div>Thu</div>
              <div>Fri</div>
              <div>Sat</div>
              <div>Sun</div>
            </div>
          </div>

          <div class="calendar-grid" id="calendarGrid">
            <!-- Calendar days will be generated here -->
          </div>
        </div>

        <!-- Today's Appointments -->
        <div class="appointments-section">
          <div class="section-header">
            <h3>📋 Today's Schedule</h3>
            <button class="btn" id="viewAllBtn">View All</button>
          </div>

          <div id="appointmentsList">
            <!-- Dynamic appointments will be loaded here -->
          </div>
        </div>
      </div>
    </div>

    <!-- New Appointment Modal -->
    <div class="modal" id="appointmentModal">
      <div class="modal-content">
        <h3>📅 New Appointment</h3>
        <form id="appointmentForm">
          <div class="form-group">
            <label>Client Name</label>
            <input
              type="text"
              id="customerNames"
              placeholder="Enter client name"
              required
            />
          </div>

          <div class="form-group">
            <label>Service</label>
            <select id="serviceSelect" required>
              <option value="">Select service</option>
              <option value="haircut">Haircut & Style - €45</option>
              <option value="coloring">Hair Coloring - €85</option>
              <option value="facial">Facial Treatment - €65</option>
              <option value="manicure">Manicure - €35</option>
            </select>
          </div>

          <div class="form-group">
            <label>Date & Time</label>
            <input type="datetime-local" id="appointmentDateTime" required />
          </div>

          <div class="form-group">
            <label>Notes</label>
            <textarea
              id="appointmentNotes"
              placeholder="Add any special notes..."
            ></textarea>
          </div>

          <div class="modal-actions">
            <button type="button" class="btn btn-secondary" id="cancelBtn">
              Cancel
            </button>
            <button type="submit" class="btn">Create Appointment</button>
          </div>
        </form>
      </div>
    </div>

    <script>
      // Sample appointments data
      let appointments = [
        {
          time: '09:00',
          client: 'Emma Wilson',
          treatment: 'Haircut & Style - 60 min',
          status: 'CONFIRMED',
          id: 1,
        },
        {
          time: '10:30',
          client: 'Sarah Johnson',
          treatment: 'Hair Coloring - 120 min',
          status: 'pending',
          id: 2,
        },
        {
          time: '14:00',
          client: 'Lisa Chen',
          treatment: 'Facial Treatment - 90 min',
          status: 'confirmed',
          id: 3,
        },
        {
          time: '15:30',
          client: 'Maria Garcia',
          treatment: 'Manicure - 45 min',
          status: 'completed',
          id: 4,
        },
        {
          time: '17:00',
          client: 'Anna Rodriguez',
          treatment: 'Hair Styling - 75 min',
          status: 'confirmed',
          id: 5,
        },
      ];

      let stats = {
        appointments: 12,
        pending: 3,
        revenue: 1245,
        clients: 8,
      };

      // Initialize
      const today = new Date();
      document.getElementById('currentDate').value = today
        .toISOString()
        .split('T')[0];

      function showNotification(message, type = 'success') {
        const notification = document.getElementById('notification');
        notification.textContent = message;
        notification.className = `notification show ${type}`;

        setTimeout(() => {
          notification.classList.remove('show');
        }, 3000);
      }

      function showLoading(duration = 1000) {
        document.getElementById('loadingOverlay').style.display = 'flex';
        setTimeout(() => {
          document.getElementById('loadingOverlay').style.display = 'none';
        }, duration);
      }

      function animateNumber(element, start, end, duration = 1000) {
        const range = end - start;
        const increment = range / (duration / 16);
        let current = start;

        const timer = setInterval(() => {
          current += increment;
          if (
            (increment > 0 && current >= end) ||
            (increment < 0 && current <= end)
          ) {
            current = end;
            clearInterval(timer);
          }

          if (element.id === 'todayRevenue') {
            element.textContent = '€' + Math.floor(current);
          } else {
            element.textContent = Math.floor(current);
          }
        }, 16);
      }

      function updateStats() {
        stats.appointments = Math.floor(Math.random() * 5) + 10;
        stats.pending = Math.floor(Math.random() * 3) + 1;
        stats.revenue += Math.floor(Math.random() * 200) + 50;
        stats.clients += Math.floor(Math.random() * 3);

        animateNumber(
          document.getElementById('todayAppointments'),
          parseInt(document.getElementById('todayAppointments').textContent),
          stats.appointments
        );
        animateNumber(
          document.getElementById('pendingCount'),
          parseInt(document.getElementById('pendingCount').textContent),
          stats.pending
        );
        animateNumber(
          document.getElementById('todayRevenue'),
          parseInt(
            document.getElementById('todayRevenue').textContent.replace('€', '')
          ),
          stats.revenue
        );
        animateNumber(
          document.getElementById('newClients'),
          parseInt(document.getElementById('newClients').textContent),
          stats.clients
        );

        // Update notification badge
        document.querySelector('.notification-badge').textContent =
          stats.pending;
      }

      function renderAppointments() {
        const container = document.getElementById('appointmentsList');
        container.innerHTML = '';

        appointments.forEach((appointment, index) => {
          const item = document.createElement('div');
          item.className = 'appointment-item';
          item.style.animationDelay = `${index * 0.1}s`;

          item.innerHTML = `
                    <div class="appointment-time">${appointment.time}</div>
                    <span class="appointment-status status-${appointment.status}">${appointment.status}</span>
                    <div class="appointment-client">${appointment.client}</div>
                    <div class="appointment-service">${appointment.service}</div>
                `;

          item.addEventListener('click', () => {
            showNotification(`Opening details for ${appointment.client}`);
            item.style.transform = 'scale(0.95)';
            setTimeout(() => {
              item.style.transform = '';
            }, 150);
          });

          container.appendChild(item);
        });
      }

      function generateCalendar() {
        const grid = document.getElementById('calendarGrid');
        const currentDate = new Date();
        const currentMonth = currentDate.getMonth();
        const currentYear = currentDate.getFullYear();

        const firstDay = new Date(currentYear, currentMonth, 1);
        const lastDay = new Date(currentYear, currentMonth + 1, 0);
        const daysInMonth = lastDay.getDate();
        const startDay = firstDay.getDay() === 0 ? 7 : firstDay.getDay();

        grid.innerHTML = '';

        // Add empty cells
        for (let i = 1; i < startDay; i++) {
          const emptyDay = document.createElement('div');
          emptyDay.className = 'calendar-day';
          grid.appendChild(emptyDay);
        }

        // Add days
        for (let day = 1; day <= daysInMonth; day++) {
          const dayElement = document.createElement('div');
          dayElement.className = 'calendar-day';
          dayElement.textContent = day;
          dayElement.style.animationDelay = `${day * 0.02}s`;

          if (day === currentDate.getDate()) {
            dayElement.classList.add('selected');
          }

          if ([8, 12, 15, 18, 22, 24, 26].includes(day)) {
            dayElement.classList.add('has-appointments');
          }

          dayElement.addEventListener('click', () => {
            document
              .querySelectorAll('.calendar-day')
              .forEach((d) => d.classList.remove('selected'));
            dayElement.classList.add('selected');
            showNotification(`Loading appointments for ${day}th`);
            showLoading(800);
          });

          grid.appendChild(dayElement);
        }
      }

      // Navigation
      document.querySelectorAll('.nav-item').forEach((item) => {
        item.addEventListener('click', () => {
          document
            .querySelectorAll('.nav-item')
            .forEach((nav) => nav.classList.remove('active'));
          item.classList.add('active');

          const section = item.dataset.section;
          document.getElementById('pageTitle').textContent =
            section.charAt(0).toUpperCase() + section.slice(1);

          showNotification(`Navigated to ${section} section`);

          if (section === 'appointments') {
            setTimeout(updateStats, 500);
          }
        });
      });

      // Stats card interactions
      document.querySelectorAll('.stat-card').forEach((card) => {
        card.addEventListener('click', () => {
          const metric = card.dataset.metric;
          showNotification(`Viewing detailed ${metric} analytics`);
          showLoading(1200);
        });
      });

      // Modal handling
      document
        .getElementById('newAppointmentBtn')
        .addEventListener('click', () => {
          document.getElementById('appointmentModal').style.display = 'block';

          // Set default date/time
          const tomorrow = new Date();
          tomorrow.setDate(tomorrow.getDate() + 1);
          tomorrow.setHours(10, 0);
          document.getElementById(
            'appointmentDateTime'
          ).value = tomorrow.toISOString().slice(0, 16);
        });

      document.getElementById('cancelBtn').addEventListener('click', () => {
        document.getElementById('appointmentModal').style.display = 'none';
      });

      document
        .getElementById('appointmentModal')
        .addEventListener('click', (e) => {
          if (e.target === e.currentTarget) {
            e.currentTarget.style.display = 'none';
          }
        });

      document
        .getElementById('appointmentForm')
        .addEventListener('submit', (e) => {
          e.preventDefault();

          const customerName = document.getElementById('customerName').value;
          const service = document.getElementById('serviceSelect').value;
          const dateTime = new Date(
            document.getElementById('appointmentDateTime').value
          );
          const notes = document.getElementById('appointmentNotes').value;

          // Add new appointment
          const newAppointment = {
            time: dateTime.toLocaleTimeString('en-US', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false,
            }),
            client: customerName,
            service: document.getElementById('serviceSelect').options[
              document.getElementById('serviceSelect').selectedIndex
            ].text,
            status: 'confirmed',
            id: appointments.length + 1,
          };

          appointments.unshift(newAppointment);

          // Update stats
          stats.appointments++;
          document.getElementById('todayAppointments').textContent =
            stats.appointments;

          showLoading(1500);
          setTimeout(() => {
            renderAppointments();
            showNotification(`Appointment created for ${customerName}!`);
          }, 1500);

          document.getElementById('appointmentModal').style.display = 'none';
          document.getElementById('appointmentForm').reset();
        });

      // Today button
      document.getElementById('todayBtn').addEventListener('click', () => {
        document.getElementById(
          'currentDate'
        ).value = today.toISOString().split('T')[0];
        generateCalendar();
        showNotification('Calendar updated to today');
      });

      // View All button
      document.getElementById('viewAllBtn').addEventListener('click', () => {
        showNotification('Loading all appointments...');
        showLoading(1000);
      });

      // Initialize
      generateCalendar();
      renderAppointments();

      // Simulate real-time updates
      setInterval(() => {
        if (Math.random() > 0.7) {
          updateStats();
        }
      }, 10000);

      // Add some dynamic behavior on load
      setTimeout(() => {
        showNotification('Welcome to Beauty CRM Dashboard!');
      }, 1000);
    </script>
  </body>
</html>
