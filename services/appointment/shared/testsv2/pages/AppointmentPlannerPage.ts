import type { Page } from 'playwright';
import { testConfig } from '../config/test-config';

export class AppointmentPlannerPage {
  constructor(private page: Page) {}

  // Selectors - centralized for maintainability
  private selectors = {
    appointmentNotes: '[data-testid="appointment-notes"]',
    bookAppointmentButton: '[data-testid="book-appointment"]',
    confirmationMessage: (message: string) => `text="${message}"`,
    customerEmail: '[data-testid="customer-email"]',
    customerName: '[data-testid="customer-name"]',
    customerPhone: '[data-testid="customer-phone"]',
    staffSelector: '[data-testid="staff-',
    submitBooking: '[data-testid="submit-booking"]',
    timeSlotPrefix: '[data-testid="time-slot-',
    treatmentSelect: '[data-testid="treatment-select"]',
  };

  async navigateToBookingPage(): Promise<void> {
    await this.page.goto(testConfig.baseUrls.plannerFrontend, {
      timeout: testConfig.timeouts.navigation,
      waitUntil: 'networkidle',
    });

    await this.page.waitForSelector(this.selectors.bookAppointmentButton, {
      timeout: testConfig.timeouts.elementWait,
    });

    await this.page.click(this.selectors.bookAppointmentButton);

    // Wait for booking form to load
    await this.page.waitForSelector(this.selectors.treatmentSelect, {
      timeout: testConfig.timeouts.elementWait,
    });

    await this.page.waitForLoadState('networkidle');
  }

  async selectTreatment(treatment: string): Promise<void> {
    await this.page.waitForSelector(this.selectors.treatmentSelect, {
      timeout: testConfig.timeouts.elementWait,
    });

    await this.page.selectOption(this.selectors.treatmentSelect, treatment);

    // Wait for stylist options to load
    await this.page.waitForTimeout(1000);
  }

  async chooseStaff(_staff: string): Promise<void> {
    const staffSelector = `${this.selectors.staffPrefix}${staff
      .replace(/\s+/g, '-')
      .toLowerCase()}"]`;

    await this.page.waitForSelector(staffSelector, {
      timeout: testConfig.timeouts.elementWait,
    });

    await this.page.click(staffSelector);

    // Wait for time slots to load
    await this.page.waitForTimeout(1000);
  }

  async selectTimeSlot(
    hour: number,
    minute: number,
    period: 'AM' | 'PM' = 'PM',
  ): Promise<void> {
    const timeSlot = `${hour}:${minute.toString().padStart(2, '0')} ${period}`;
    const timeSlotSelector = `${this.selectors.timeSlotPrefix}${timeSlot}"]`;

    await this.page.waitForSelector(timeSlotSelector, {
      timeout: testConfig.timeouts.elementWait,
    });

    await this.page.click(timeSlotSelector);
  }

  async fillContactDetails(details: {
    name: string;
    email: string;
    phone: string;
  }): Promise<void> {
    // Fill name
    await this.page.waitForSelector(this.selectors.customerName, {
      timeout: testConfig.timeouts.elementWait,
    });
    await this.page.fill(this.selectors.customerName, details.name);

    // Fill email
    await this.page.waitForSelector(this.selectors.customerEmail, {
      timeout: testConfig.timeouts.elementWait,
    });
    await this.page.fill(this.selectors.customerEmail, details.email);

    // Fill phone
    await this.page.waitForSelector(this.selectors.customerPhone, {
      timeout: testConfig.timeouts.elementWait,
    });
    await this.page.fill(this.selectors.customerPhone, details.phone);
  }

  async addNotes(notes: string): Promise<void> {
    await this.page.waitForSelector(this.selectors.appointmentNotes, {
      timeout: testConfig.timeouts.elementWait,
    });

    await this.page.fill(this.selectors.appointmentNotes, notes);
  }

  async submitBooking(): Promise<void> {
    await this.page.waitForSelector(this.selectors.submitBooking, {
      timeout: testConfig.timeouts.elementWait,
    });

    await this.page.click(this.selectors.submitBooking);

    // Wait for submission to complete
    await this.page.waitForTimeout(1000);
  }

  async waitForConfirmationMessage(message: string): Promise<boolean> {
    try {
      const messageLocator = this.page.locator(
        this.selectors.confirmationMessage(message),
      );

      await messageLocator.waitFor({
        state: 'visible',
        timeout: testConfig.timeouts.assertion,
      });

      return await messageLocator.isVisible();
    } catch (error) {
      console.error(`Failed to find confirmation message: ${message}`, error);
      return false;
    }
  }

  // Health check for the service
  async isServiceHealthy(): Promise<boolean> {
    try {
      const response = await this.page.goto(
        `${testConfig.baseUrls.plannerFrontend}/health`,
      );
      return response?.ok() || false;
    } catch {
      return false;
    }
  }
}
