/**
 * Sprint 5 E2E Tests - Real-Time Appointment Synchronization
 * QA Lead: Dr<PERSON> <PERSON> - NASA-Grade Testing Standards
 *
 * These tests validate the core Sprint 5 goal:
 * "Appointments booked through planner are immediately visible in management UI"
 */

import { type BrowserContext, expect, type Page, test } from '@playwright/test';

// Test configuration
const SYNC_TIMEOUT = 2000; // 2 seconds max sync time
const PLANNER_URL = process.env.PLANNER_URL || 'http://localhost:3001';
const MANAGEMENT_URL = process.env.MANAGEMENT_URL || 'http://localhost:3002';

interface AppointmentData {
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  service: string;
  staff: string;
  datetime: string;
  notes: string;
}

class PlannerPage {
  constructor(private page: Page) {}

  async navigate() {
    await this.page.goto(PLANNER_URL);
    await expect(
      this.page.locator('[data-testid="booking-form"]'),
    ).toBeVisible();
  }

  async selectTreatment(treatmentName: string) {
    await this.page.selectOption(
      '[data-testid="treatment-select"]',
      treatmentName,
    );
  }

  async selectStylist(staffName: string) {
    await this.page.selectOption('[data-testid="stylist-select"]', staffName);
  }

  async selectDateTime(datetime: string) {
    await this.page.fill('[data-testid="datetime-input"]', datetime);
  }

  async fillContactDetails(details: {
    name: string;
    email: string;
    phone: string;
  }) {
    await this.page.fill('[data-testid="client-name"]', details.name);
    await this.page.fill('[data-testid="client-email"]', details.email);
    await this.page.fill('[data-testid="client-phone"]', details.phone);
  }

  async addNotes(notes: string) {
    await this.page.fill('[data-testid="appointment-notes"]', notes);
  }

  async submitBooking() {
    const responsePromise = this.page.waitForResponse(
      (response) =>
        response.url().includes('/api/appointments') &&
        response.status() === 201,
    );

    await this.page.click('[data-testid="submit-booking"]');
    await responsePromise;
  }

  async waitForConfirmation() {
    await expect(
      this.page.locator('[data-testid="booking-confirmed"]'),
    ).toBeVisible();
    const confirmationText = await this.page
      .locator('[data-testid="confirmation-message"]')
      .textContent();
    expect(confirmationText).toContain('Booking Confirmed!');
  }
}

class ManagementPage {
  constructor(private page: Page) {}

  async navigate() {
    await this.page.goto(MANAGEMENT_URL);
    await expect(
      this.page.locator('[data-testid="appointment-calendar"]'),
    ).toBeVisible();
  }

  async waitForNewAppointment(
    customerName: string,
    maxWaitTime: number = SYNC_TIMEOUT,
  ) {
    const startTime = Date.now();

    await this.page.waitForSelector(
      `[data-testid="appointment-item"][data-client="${customerName}"]`,
      { timeout: maxWaitTime },
    );

    const syncTime = Date.now() - startTime;
    console.log(`Appointment sync time: ${syncTime}ms`);
    expect(syncTime).toBeLessThan(maxWaitTime);

    return syncTime;
  }

  async getAppointmentDetails(customerName: string) {
    const appointmentElement = this.page.locator(
      `[data-testid="appointment-item"][data-client="${customerName}"]`,
    );

    return {
      customerName: await appointmentElement.getAttribute('data-customer'),
      datetime: await appointmentElement.getAttribute('data-datetime'),
      staff: await appointmentElement.getAttribute('data-stylist'),
      status: await appointmentElement.getAttribute('data-status'),
      treatmentName: await appointmentElement.getAttribute('data-treatment'),
    };
  }

  async confirmAppointment(customerName: string, staffNote: string) {
    await this.page.click(
      `[data-testid="appointment-item"][data-client="${customerName}"]`,
    );
    await this.page.selectOption('[data-testid="status-select"]', 'CONFIRMED');
    await this.page.fill('[data-testid="staff-notes"]', staffNote);

    const responsePromise = this.page.waitForResponse(
      (response) =>
        response.url().includes('/api/appointments') &&
        response.request().method() === 'PUT',
    );

    await this.page.click('[data-testid="save-changes"]');
    await responsePromise;
  }
}

test.describe('Sprint 5: Real-Time Appointment Synchronization', () => {
  let plannerContext: BrowserContext;
  let managementContext: BrowserContext;
  let plannerPage: Page;
  let managementPage: Page;

  test.beforeEach(async ({ browser }) => {
    // Create separate browser contexts for planner and management
    plannerContext = await browser.newContext();
    managementContext = await browser.newContext();

    plannerPage = await plannerContext.newPage();
    managementPage = await managementContext.newPage();

    // Setup error tracking
    plannerPage.on('console', (msg) => {
      if (msg.type() === 'error') {
        console.error('Planner Error:', msg.text());
      }
    });

    managementPage.on('console', (msg) => {
      if (msg.type() === 'error') {
        console.error('Management Error:', msg.text());
      }
    });
  });

  test.afterEach(async () => {
    await plannerContext.close();
    await managementContext.close();
  });

  test('Lisa Wong books appointment - immediate visibility in management UI', async () => {
    const planner = new PlannerPage(plannerPage);
    const management = new ManagementPage(managementPage);

    // Setup management page to monitor for new appointments
    await management.navigate();

    // Lisa Wong books appointment through planner
    await planner.navigate();
    await planner.selectService('Hair Color & Cut');
    await planner.selectStylist('Miguel Rodriguez');

    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(14, 0, 0, 0); // 2:00 PM

    await planner.selectDateTime(tomorrow.toISOString().slice(0, 16));
    await planner.fillContactDetails({
      email: '<EMAIL>',
      name: 'Lisa Wong',
      phone: '******-0123',
    });
    await planner.addNotes('First-time client, prefers natural colors');

    // Submit booking and verify confirmation
    await planner.submitBooking();
    await planner.waitForConfirmation();

    // Verify appointment appears in management within 2 seconds
    const syncTime = await management.waitForNewAppointment(
      'Lisa Wong',
      SYNC_TIMEOUT,
    );

    // Validate data consistency
    const appointmentDetails =
      await management.getAppointmentDetails('Lisa Wong');
    expect(appointmentDetails.customerName).toBe('Lisa Wong');
    expect(appointmentDetails.treatmentName).toBe('Hair Color & Cut');
    expect(appointmentDetails.staffId).toBe('Miguel Rodriguez');
    expect(appointmentDetails.status).toBe('pending');

    console.log(`✅ Sprint 5 Goal Achieved: Sync completed in ${syncTime}ms`);
  });

  test('Sarah Johnson confirms appointment - status updates in planner', async () => {
    const planner = new PlannerPage(plannerPage);
    const management = new ManagementPage(managementPage);

    // First, create an appointment through planner
    await planner.navigate();
    await planner.selectService('Premium Facial');
    await planner.selectStylist('Elena Rodriguez');

    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(10, 0, 0, 0);

    await planner.selectDateTime(tomorrow.toISOString().slice(0, 16));
    await planner.fillContactDetails({
      email: '<EMAIL>',
      name: 'Lisa Wong',
      phone: '******-0123',
    });

    await planner.submitBooking();
    await planner.waitForConfirmation();

    // Sarah Johnson confirms appointment in management
    await management.navigate();
    await management.waitForNewAppointment('Lisa Wong');
    await management.confirmAppointment(
      'Lisa Wong',
      'Confirmed by Sarah - prepared for first-time client',
    );

    // Verify status updates back to planner
    await planner.page.reload();
    await expect(
      planner.page.locator(
        '[data-testid="appointment-status"][data-status="confirmed"]',
      ),
    ).toBeVisible({ timeout: SYNC_TIMEOUT });
  });

  test('Data consistency validation across systems', async () => {
    const testData: AppointmentData = {
      customerEmail: '<EMAIL>',
      customerName: 'Dr. Sarah Mitchell',
      customerPhone: '******-NASA',
      datetime: '2025-05-25T10:00:00',
      notes: 'QA testing scenario - comprehensive',
      treatment: 'Executive Wellness Consultation',
      staff: 'Elena Rodriguez',
    };

    const planner = new PlannerPage(plannerPage);
    const management = new ManagementPage(managementPage);

    // Create appointment in planner
    await planner.navigate();
    await planner.selectTreatment(testData.treatment);
    await planner.selectStylist(testData.staff);
    await planner.selectDateTime(testData.datetime);
    await planner.fillContactDetails({
      email: testData.customerEmail,
      name: testData.customerName,
      phone: testData.customerPhone,
    });
    await planner.addNotes(testData.notes);
    await planner.submitBooking();
    await planner.waitForConfirmation();

    // Verify in management system
    await management.navigate();
    const syncTime = await management.waitForNewAppointment(
      testData.customerName,
    );

    const managementData = await management.getAppointmentDetails(
      testData.customerName,
    );

    // Validate 100% data consistency
    expect(managementData.customerName).toBe(testData.customerName);
    expect(managementData.treatment).toBe(testData.treatment);
    expect(managementData.staff).toBe(testData.staff);

    console.log(`✅ Data Consistency: 100% - Sync Time: ${syncTime}ms`);
    expect(syncTime).toBeLessThan(SYNC_TIMEOUT);
  });

  test('Conflict detection - prevent double booking', async () => {
    const planner = new PlannerPage(plannerPage);
    const management = new ManagementPage(managementPage);

    const conflictDateTime = new Date();
    conflictDateTime.setDate(conflictDateTime.getDate() + 1);
    conflictDateTime.setHours(14, 0, 0, 0);

    // Lisa books Miguel at 2:00 PM
    await planner.navigate();
    await planner.selectService('Hair Cut');
    await planner.selectStylist('Miguel Rodriguez');
    await planner.selectDateTime(conflictDateTime.toISOString().slice(0, 16));
    await planner.fillContactDetails({
      email: '<EMAIL>',
      name: 'Lisa Wong',
      phone: '******-0123',
    });
    await planner.submitBooking();
    await planner.waitForConfirmation();

    // Try to book same stylist at same time through management
    await management.navigate();
    await management.waitForNewAppointment('Lisa Wong');

    // Attempt double booking should fail
    await managementPage.goto(`${MANAGEMENT_URL}/book`);
    await managementPage.selectOption(
      '[data-testid="stylist-select"]',
      'Miguel Rodriguez',
    );
    await managementPage.fill(
      '[data-testid="datetime-input"]',
      conflictDateTime.toISOString().slice(0, 16),
    );
    await managementPage.fill('[data-testid="client-name"]', 'Emma Chen');

    await managementPage.click('[data-testid="submit-booking"]');

    // Should show conflict message
    await expect(
      managementPage.locator('[data-testid="error-message"]'),
    ).toContainText('Time slot no longer available');
  });

  test('Performance under load - multiple simultaneous bookings', async () => {
    const bookingPromises: Promise<void>[] = [];
    const syncTimes: number[] = [];

    // Create 5 simultaneous bookings
    for (let i = 0; i < 5; i++) {
      const bookingPromise = (async () => {
        const browser = plannerContext.browser();
        if (!browser) throw new Error('Browser not available');

        const context = await browser.newContext();
        const page = await context.newPage();
        const planner = new PlannerPage(page);

        const bookingTime = new Date();
        bookingTime.setDate(bookingTime.getDate() + 1);
        bookingTime.setHours(10 + i, 0, 0, 0); // Different times to avoid conflicts

        const startTime = Date.now();

        await planner.navigate();
        await planner.selectService('Basic Haircut');
        await planner.selectStylist('Miguel Rodriguez');
        await planner.selectDateTime(bookingTime.toISOString().slice(0, 16));
        await planner.fillContactDetails({
          email: `client${i + 1}@example.com`,
          name: `Client ${i + 1}`,
          phone: `******-000${i + 1}`,
        });
        await planner.submitBooking();
        await planner.waitForConfirmation();

        const syncTime = Date.now() - startTime;
        syncTimes.push(syncTime);
        console.log(`Client ${i + 1} booking completed in ${syncTime}ms`);

        await context.close();
      })();

      bookingPromises.push(bookingPromise);
    }

    // Wait for all bookings to complete
    await Promise.all(bookingPromises);

    // Validate all sync times are under threshold
    for (const syncTime of syncTimes) {
      expect(syncTime).toBeLessThan(SYNC_TIMEOUT);
    }

    const avgSyncTime = syncTimes.reduce((a, b) => a + b, 0) / syncTimes.length;
    console.log(`✅ Load Test Completed - Average Sync Time: ${avgSyncTime}ms`);
  });

  test('Mobile responsiveness - Sarah Johnson mobile workflow', async () => {
    // Set mobile viewport
    await managementPage.setViewportSize({ height: 667, width: 375 }); // iPhone SE

    const management = new ManagementPage(managementPage);
    await management.navigate();

    // Verify mobile-responsive elements
    await expect(
      managementPage.locator('[data-testid="mobile-navigation"]'),
    ).toBeVisible();
    await expect(
      managementPage.locator('[data-testid="appointment-calendar"]'),
    ).toBeVisible();

    // Test touch interactions
    await managementPage.tap('[data-testid="new-appointment-button"]');
    await expect(
      managementPage.locator('[data-testid="mobile-booking-form"]'),
    ).toBeVisible();
  });

  test('Error handling and recovery', async () => {
    const planner = new PlannerPage(plannerPage);

    // Simulate network failure during booking
    await plannerPage.route('**/api/appointments', (route) => {
      route.abort(); // Simulate network error
    });

    await planner.navigate();
    await planner.selectService('Hair Cut');
    await planner.selectStylist('Miguel Rodriguez');

    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(10, 0, 0, 0);

    await planner.selectDateTime(tomorrow.toISOString().slice(0, 16));
    await planner.fillContactDetails({
      email: '<EMAIL>',
      name: 'Test Client',
      phone: '******-TEST',
    });

    await planner.submitBooking();

    // Should show error handling
    await expect(
      plannerPage.locator('[data-testid="booking-error"]'),
    ).toContainText('Booking in progress...');

    // Restore network and verify retry
    await plannerPage.unroute('**/api/appointments');

    // Should eventually succeed
    await planner.waitForConfirmation();
  });

  test('Performance validation - sync within 2 seconds', async () => {
    const planner = new PlannerPage(plannerPage);
    const management = new ManagementPage(managementPage);

    // Setup management monitoring
    await management.navigate();

    // Create appointment
    await planner.navigate();
    await planner.selectService('Hair Cut');
    await planner.selectStylist('Miguel Rodriguez');

    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(15, 0, 0, 0);

    await planner.selectDateTime(tomorrow.toISOString().slice(0, 16));
    await planner.fillContactDetails({
      email: '<EMAIL>',
      name: 'Performance Test Client',
      phone: '******-PERF',
    });

    const _startTime = Date.now();
    await planner.submitBooking();
    await planner.waitForConfirmation();

    // Measure sync performance
    const syncTime = await management.waitForNewAppointment(
      'Performance Test Client',
      SYNC_TIMEOUT,
    );

    console.log(`🚀 Performance Test: ${syncTime}ms sync time`);
    expect(syncTime).toBeLessThan(2000); // Must be under 2 seconds
    expect(syncTime).toBeLessThan(1500); // Target: sub-1.5s for excellence
  });
});
