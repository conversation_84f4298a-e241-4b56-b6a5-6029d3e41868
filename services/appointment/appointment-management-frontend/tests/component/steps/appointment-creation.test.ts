import { describe, expect, it } from 'vitest';

describe('Appointment Creation', () => {
  it('should validate client details', () => {
    // Mock client details
    const validClient = {
      email: '<EMAIL>',
      name: '<PERSON>',
      phone: '1234567890',
    };

    const invalidClient = {
      email: 'invalid-email',
      name: '<PERSON>',
      phone: 'invalid',
    };

    // Validation functions
    const isValidEmail = (email: string) =>
      /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    const isValidPhone = (phone: string) => /^\d{10}$/.test(phone);

    // Assertions for valid client
    expect(isValidEmail(validClient.email)).toBe(true);
    expect(isValidPhone(validClient.phone)).toBe(true);

    // Assertions for invalid client
    expect(isValidEmail(invalidClient.email)).toBe(false);
    expect(isValidPhone(invalidClient.phone)).toBe(false);
  });

  it('should check for time slot availability', () => {
    // Mock time slots
    const bookedTimeSlots = ['2023-06-15T14:00:00'];
    const availableTimeSlot = '2023-06-15T15:00:00';
    const unavailableTimeSlot = '2023-06-15T14:00:00';

    // Check availability
    const isTimeSlotAvailable = (timeSlot: string) =>
      !bookedTimeSlots.includes(timeSlot);

    // Assertions
    expect(isTimeSlotAvailable(availableTimeSlot)).toBe(true);
    expect(isTimeSlotAvailable(unavailableTimeSlot)).toBe(false);
  });

  it('should create an appointment with valid details', () => {
    // Mock data
    const selectedService = 'Haircut';
    const selectedTimeSlot = '2023-06-15T15:00:00';
    const clientDetails = {
      email: '<EMAIL>',
      name: 'John Doe',
      phone: '1234567890',
    };

    // Create appointment
    const appointment = {
      client: clientDetails,
      treatment: selectedService,
      timeSlot: selectedTimeSlot,
    };

    // Assertions
    expect(appointment).toEqual({
      client: {
        email: '<EMAIL>',
        name: 'John Doe',
        phone: '1234567890',
      },
      treatment: 'Haircut',
      timeSlot: '2023-06-15T15:00:00',
    });
  });
});
