import { useQuery } from '@tanstack/react-query';
import { salonApi, staffApi, treatmentApi } from '../utils/api';

// Hook for fetching all salons
export const useSalons = () => {
  return useQuery({
    queryFn: salonApi.getAll,
    queryKey: ['salons'],
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook for fetching a specific salon
export const useSalon = (salonId: string) => {
  return useQuery({
    enabled: !!salonId,
    queryFn: () => salonApi.getById(salonId),
    queryKey: ['salon', salonId],
    staleTime: 5 * 60 * 1000,
  });
};

// Hook for fetching salon staff (cross-service)
export const useSalonStaff = (salonId: string) => {
  return useQuery({
    enabled: !!salonId,
    queryFn: () => staffApi.getBySalon(salonId),
    queryKey: ['salon-staff', salonId],
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Hook for fetching salon treatments (cross-service)
export const useSalonTreatments = (salonId: string) => {
  return useQuery({
    enabled: !!salonId,
    queryFn: () => treatmentApi.getAll(salonId),
    queryKey: ['salon-treatments', salonId],
    staleTime: 5 * 60 * 1000,
  });
};

// Hook for dashboard analytics (mock data for now)
export const useSalonAnalytics = (salonId: string) => {
  return useQuery({
    enabled: !!salonId,
    queryFn: async () => {
      // Mock analytics data - in real app this would come from analytics service
      return {
        completionRate: Math.floor(Math.random() * 20) + 80,
        newCustomers: Math.floor(Math.random() * 10) + 1,
        recentAppointments: [
          {
            customerName: 'Sarah Johnson',
            id: '1',
            treatment: 'Haircut & Color',
            status: 'CONFIRMED',
            time: '2:00 PM',
          },
          {
            customerName: 'Mike Chen',
            id: '2',
            treatment: 'Beard Trim',
            status: 'pending',
            time: '3:30 PM',
          },
          {
            customerName: 'Emma Davis',
            id: '3',
            treatment: 'Manicure',
            status: 'CONFIRMED',
            time: '4:00 PM',
          },
        ],
        todayAppointments: Math.floor(Math.random() * 30) + 10,
        todayRevenue: Math.floor(Math.random() * 3000) + 1000,
      };
    },
    queryKey: ['salon-analytics', salonId],
    staleTime: 1 * 60 * 1000, // 1 minute for analytics
  });
};
